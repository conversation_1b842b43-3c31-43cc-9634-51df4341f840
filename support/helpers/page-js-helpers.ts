import { ExchangeRatesResponse, TCurrency, TCurrencyAccountSelect } from '../types';
import { Page } from '@playwright/test';
import { Settings } from '../settings';

/**
 * Retrieves the user authentication token from the local storage of page.
 */
export const getUserToken = async (page: Page): Promise<string> => {
  const token = await page.evaluate(() => {
    return localStorage.getItem('auth._token.local');
  });
  if (!token) {
    throw new Error('User is not logged in');
  }
  return token;
};

/**
 * Sets the user authentication token in the local storage of page.
 */
export const setUserToken = async (page: Page, token: string) => {
  const withBearer = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
  await page.evaluate((token) => {
    localStorage.setItem('auth._token.local', token);
  }, withBearer);
};

/**
 * Fetches the real-time exchange rate for a specific currency relative to USD.
 * For precise conversion.
 */
export const getExchangeRates = async (
  page: Page,
  currency: TCurrency = TCurrencyAccountSelect.USDT
) => {
  const url = Settings.backendApiUrl + '/user/exchange-rates';
  const res = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${await getUserToken(page)}`,
    },
  });
  const response: ExchangeRatesResponse = await res.json();
  const { data: rates } = response;
  return Number(rates[currency]['USD'] || '1');
};

/**
 * Clear the November experiment value.
 * TODO: Remove this function after the experiment ends.
 */
export const clearNovemberExperiment = async (page: Page) => {
  const url = Settings.backendApiUrl + '/remote-storage';
  await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${await getUserToken(page)}`,
    },
    body: JSON.stringify({
      key: 'backtonovember',
      value: 0,
    }),
  });
};

/**
 * After login.
 * Here we should set experiment values.
 */
export const afterLoginActions = async (page: Page) => {
  await clearNovemberExperiment(page);
  // console.log('afterLoginActions');
};

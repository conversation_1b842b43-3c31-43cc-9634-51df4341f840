import { randomEmail } from './random-helpers';
import { Settings } from '../settings';

export const apiUserRegister = async (email = '', password = '') => {
  const userEmail = email.length > 0 ? email : randomEmail();
  const userPassword = password.length > 0 ? password : 'password';
  const payload = {
    email: userEmail,
    password: userPassword,
  };

  const res = await fetch(Settings.backendApiUrl + '/user/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(payload),
  });
  return await res.json();
};

export const apiUserUSDTAddress = async (token: string) => {
  const url = Settings.backendApiUrl + '/account';
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });
  const result = await response.json();
  if (!result || !result.data) {
    throw new Error('Invalid response from /account endpoint');
  }
  const usdtAccount = result.data.find(
    (account: { currency_id: number }) => account.currency_id === 15
  );
  if (!usdtAccount) {
    throw new Error('USDT account not found');
  }
  const usdtAddress = usdtAccount.addresses[0].address;
  if (!usdtAddress) {
    throw new Error('USDT address not found');
  }
  return usdtAddress;
};

export const apiUserLogin = async (email: string, password: string): Promise<{ token: string }> => {
  const url = Settings.backendApiUrl + '/user/login';
  const res = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify({
      email,
      password,
    }),
  });
  if (!res.ok) {
    console.log('Error logging in');
  }
  const json = await res.json();
  console.log('Success login ', json);
  return json;
};

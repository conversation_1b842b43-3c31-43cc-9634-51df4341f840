import { Settings } from "../settings";

/**
 * experiment keys:
 * 'backtonovember'
 */
export enum ExperimentKeys {
  BackToNovember = 'backtonovember',
  DEPOSIT_V2 = "deposit_v2",
}


/**
 * We want to set experiment values for user.
 * To do that we need to send request to /remote-storage endpoint
 * `PUT /remote-storage { key, value }`
 */
export const apiUserSetExperiment = async (token: string, key: ExperimentKeys, value: any) => {
  const url = Settings.backendApiUrl + '/remote-storage';
  const res = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({
      key: key,
      value: value,
    }),
  });
  return await res.json();
};

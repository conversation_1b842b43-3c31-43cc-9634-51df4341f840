import { TCountrySet, VerificationTier } from '../types';
import { Settings } from '../settings';
import { randomString } from './random-helpers';

/**
 * Creates a payment by sending a POST request to the emulate API.
 * 10 confirmations.
 * USDT.
 *
 * @param {string} address - The recipient's address for the payment.
 * @param {string} [amount=10000] - The amount of the payment. Defaults to the value defined in `Settings.defaultAmount`.
 * @throws {Error} If the API request fails or returns a non-successful HTTP status.
 * @returns {Promise<void>}
 */
export const paymentCreate = async (
  address: string,
  amount: string = Settings.defaultAmount.toString()
): Promise<void> => {
  const createPaymentUrl = Settings.emulateApiUrl + '/payment/create';

  const res = await fetch(createPaymentUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify({
      address,
      confirmations: 10,
      ticker: 'USDT',
      amount,
    }),
  });

  if (!res.ok) {
    throw new Error(`Error creating payment: ${res.statusText}.`);
  }
};

export type TCreateUserPayload = {
  user: {
    email: string;
    password: string;
    master_id?: number;
    type?: number;
  };
  verification?: {
    slug?: VerificationTier;
    first_name?: string;
    last_name?: string;
    birthday?: string;
    country_iso3?: string;
  };
  country_set?: {
    id: TCountrySet;
  };
  is_suspicious?: boolean;
  show_warn?: boolean;
};

/**
 * Creates a new user by making a POST request to the emulated API endpoint.
 *
 * @param {TCreateUserPayload} payload - The payload containing user data to be sent in the request body.
 * @returns {Promise<void>} A promise that resolves when the request is complete.
 */
export const createUserEmulate = async (
  payload: TCreateUserPayload
): Promise<TCreateUserPayload> => {
  const createUserUrl = Settings.emulateApiUrl + '/user-register/site';

  const res = await fetch(createUserUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (res.ok) {
    console.log('User created successfully', payload);
    const response = await res.json();
    console.log('Response from API', response);
  }
  return payload;
};

export enum PromocodeType {
  NoAction = 0,
  CardDiscount = 20,
  SubscriptionDiscount = 30,
  FreeExtension = 40,
}

/**
 * Creates a card discount promocode with emulate API.
 *
 * @param code - Optional. Promocode string.
 * @param cardBuyDiscount - Optional. The discount percentage to apply when buying a card. Defaults to 50.
 * @param cardBonusAmount - Optional. The bonus amount to apply to the card. Defaults to 15.
 * @returns A promise promocode string.
 */
export const createCardPromocodeEmulate = async (
  code: string = '',
  cardBuyDiscount: number = 50,
  cardBonusAmount: number = 15
): Promise<string> => {
  const promocode = code || `testpromo${randomString(4)}`;
  const createPromocodeUrl = Settings.emulateApiUrl + '/promocode/create';

  const payload = {
    type: PromocodeType.CardDiscount,
    code: promocode,
    card_buy_discount_percent: cardBuyDiscount.toString(),
    card_bonus_amount: cardBonusAmount.toString(),
  };

  const res = await fetch(createPromocodeUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    throw new Error(`Error creating promocode: ${res.statusText}.`);
  }
  return promocode;
};

import { VerificationButtonWidget } from './widgets/VerificationButtonWidget';
import { AccountsWidget } from './widgets/AccountsWidget';
import { Page } from '@playwright/test';
import { BasePage } from './BasePage';
import { LeftMenuWidget } from './widgets/LeftMenuWidget';

export class DashboardPage extends BasePage {
  path = '/app/dashboard';

  static CARDS_TABLE_ID = 'dashboard.cards-table';
  static CARD_ITEM_ID = 'table-card-item';
  static VERIFICATION_WIDGET_ICON_ID = 'verification-widget-icon';
  static VERIFICATION_CIRCLE_MOBILE_ICON_ID = 'verification-circle-mobile-icon';
  static USER_WIDGET_UNLIMITED_ICON_ID = 'user-widget-unlimited-icon';

  static CARD_AUTO_REFILL_ICON_ID = 'card-auto-refill-icon';

  cardsTable = this.page.getByTestId(DashboardPage.CARDS_TABLE_ID).describe('Cards table');
  newCardButton = this.cardsTable.getByTestId('new-card-issue-btn').describe('New card button');

  issueUltimaCardButton = this.page
    .getByTestId('issue-ultima-card-button')
    .describe('Issue Ultima card button');

  issueAdvCardButton = this.page
    .getByTestId('issue-adv-card-button')
    .describe('Issue Adv card button');

  subscriptionBlock = this.page.getByTestId('subscription-block');

  constructor(page: Page) {
    super(page);
  }

  get accountCards() {
    return new AccountsWidget(this.page);
  }

  get leftMenu() {
    return new LeftMenuWidget(this.page);
  }

  get verificationButton() {
    return new VerificationButtonWidget(this.page);
  }

  async clickIssueUltimaCard() {
    await this.issueUltimaCardButton.click();
  }

  async clickIssueAdvCard() {
    await this.issueAdvCardButton.click();
  }
}

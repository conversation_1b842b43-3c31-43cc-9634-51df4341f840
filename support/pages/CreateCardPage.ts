import { DefaultCardIssueStep } from './widgets/issue-card/steps/DefaultCardIssueStep';
import { SelectCardTariffStep } from './widgets/issue-card/steps/SelectCardTariffStep';
import { Page } from '@playwright/test';
import { BasePage } from './BasePage';
import { SelectCardBinStep } from './widgets/issue-card/steps/SelectCardBinStep';
import { CardApproveStep } from './widgets/issue-card/steps/CardApproveStep';
import { SelectCardTypeStep } from './widgets/issue-card/steps/SelectCardTypeStep';
import { UltimaWithoutSubscriptionCardIssueStep } from './widgets/issue-card/steps/UltimaWithoutSubscriptionCardIssueStep';

export class CreateCardPage extends BasePage {
  path = '/app/create';

  createCardSuccessButton = this.page.getByTestId('create-card-success-button');

  createCardConfirmButton = this.page
    .getByTestId('create-card-confirm-button')
    .describe('Confirm button');

  // confirm card step
  enabledAutoRefillElement = this.page
    .getByTestId('issue-card.confirm-auto-refill-on')
    .describe('Enabled auto refill element');

  disabledAutoRefillElement = this.page.getByTestId('issue-card.confirm-auto-refill-off');

  constructor(page: Page) {
    super(page);
  }

  get selectCardTypeStep() {
    return new SelectCardTypeStep(this.page);
  }

  get selectCardTariffStep() {
    return new SelectCardTariffStep(this.page);
  }

  get selectBinStep() {
    return new SelectCardBinStep(this.page);
  }

  get defaultCardIssueStep() {
    return new DefaultCardIssueStep(this.page);
  }

  get cardApproveStep() {
    return new CardApproveStep(this.page);
  }

  get ultimaWithoutSubscriptionCardIssueStep() {
    return new UltimaWithoutSubscriptionCardIssueStep(this.page);
  }

  async clickContinue() {
    await this.createCardSuccessButton.click();
  }

  async clickConfirm() {
    await this.createCardConfirmButton.click();
  }
}

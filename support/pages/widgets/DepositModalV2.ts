import { Widget } from './Widget';

export class DepositModalV2 extends Widget {
  async getUSDTDepositAddress(): Promise<string> {
    const addressEl = this.page.getByTestId('deposit-address-block')
    // assert we have address element
    await addressEl.waitFor({ state: 'visible' });
    const address = String(await addressEl.textContent());
    if (!address) throw new Error('Deposit address is empty');
    return address;
  }
}

import { Widget } from './Widget';

export class DepositModal extends Widget {
  depositAddressElement = this.page.getByTestId('deposit-address-block').locator('p.copy-text').describe('Deposit address element');
  
  async getCryptoDepositAddress() {
    await this.depositAddressElement.waitFor({ state: 'visible' });
    const address = await this.depositAddressElement.textContent();
    if (!address) throw new Error('Deposit address is empty');
    return address;
  }
}

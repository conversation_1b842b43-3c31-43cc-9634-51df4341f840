import { Widget } from "../Widget";
import { Page } from "@playwright/test";

export enum SubscriptionBuyTabsEnum {
  Monthly = 1,
  Quarterly = 2,
  Semiannual = 3,
}

export class SubscriptionBuyTabs extends Widget {
  constructor(page: Page) {
    super(page);
  }
  static ID = 'subscriptions-buy-tabs';

  widget = this.page.getByTestId(SubscriptionBuyTabs.ID);

  // all 3 tabs have same test id: subscriptions-buy-tabs-tab
  tabs = this.widget.getByTestId('subscriptions-buy-tabs-tab');

  async selectTab(tab: SubscriptionBuyTabsEnum) {
    await this.tabs.nth(tab - 1).click();
    await this.page.waitForTimeout(1000); // wait for animation
  }
}
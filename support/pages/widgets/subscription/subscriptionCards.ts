import { Widget } from '../Widget';
import { SubscriptionTierTitle } from '../../../types';
import { Page } from '@playwright/test';

export class SubscriptionCards extends Widget {
  static SUBSCRIPTION_TARIFF_CARD_ID = 'subscriptions-tariff-card';
  static TARIFF_NAME_ID = 'tariff-name';
  static TARIFF_PRICE_ID = 'tariff-price';
  static SELECT_TARIFF_BUTTON_ID = 'select-tariff-button';

  static TARIFF_TABS_ID = 'subscription.tariff-tabs';
  static TARIFF_TAB_MONTHLY_ID = 'subscription.tariff-tab.monthly';
  static TARIFF_TAB_QUARTERLY_ID = 'subscription.tariff-tab.quarterly';
  static TARIFF_TAB_SEMIANNUAL_ID = 'subscription.tariff-tab.semiannual';

  constructor(page: Page) {
    super(page);
  }

  getTariffCards() {
    return this.page.getByTestId(SubscriptionCards.SUBSCRIPTION_TARIFF_CARD_ID);
  }

  async getTariffCardByName(tariffName: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall) {
    return this.getTariffCards()
      .filter({
        has: this.page
          .getByTestId(SubscriptionCards.TARIFF_NAME_ID)
          .getByText(tariffName, { exact: true }),
      })
      .first()
      .describe(`tariff card with name ${tariffName}`);
  }

  async selectTariffByName(tariffName: SubscriptionTierTitle) {
    const card = await this.getTariffCardByName(tariffName);
    const button = card
      .getByTestId(SubscriptionCards.SELECT_TARIFF_BUTTON_ID)
      .describe(`Select ${tariffName} tariff button`);
    await button.click();
  }

  async selectTariffTab(tabName: 'monthly' | 'quarterly' | 'semiannual') {
    let tabId;
    switch (tabName) {
      case 'monthly':
        tabId = SubscriptionCards.TARIFF_TAB_MONTHLY_ID;
        break;
      case 'quarterly':
        tabId = SubscriptionCards.TARIFF_TAB_QUARTERLY_ID;
        break;
      case 'semiannual':
        tabId = SubscriptionCards.TARIFF_TAB_SEMIANNUAL_ID;
        break;
    }
    await this.page.getByTestId(tabId).describe(`selected tab ${tabName}`).click();
    await this.page.waitForTimeout(1000); // wait for animation
  }

  async getDisplayedCardsNames() {
    return this.getTariffCards().getByTestId(SubscriptionCards.TARIFF_NAME_ID).allTextContents();
  }

  async getDisplayedCardsPrices() {
    return this.getTariffCards().getByTestId(SubscriptionCards.TARIFF_PRICE_ID).allTextContents();
  }
}

import { Page } from '@playwright/test';
import { Widget } from '../Widget';

export class AccountSelect extends Widget {
  static ID = 'accounts-and-cards-select';

  widget = this.page.getByTestId(AccountSelect.ID);

  selectedOption = this.widget.getByTestId('ui-select').describe('Selected option');

  selectedOptionBalance = this.widget
    .getByTestId('account-balance')
    .describe('Selected option balance');


  selectedOptionV2 = this.page.getByTestId('accounts-select-v2-selected-option').describe('Selected option v2');
  selectedOptionV2Balance = this.page.getByTestId('account-select-item-default-balance').describe('Selected option v2 balance');


  options = this.widget
    .getByTestId('accounts-and-cards-select-option-account')
    .describe('Account select options');

  error = this.page.getByTestId('ui-select-error').describe('Select error message');

  errorV2 = this.page.getByTestId('ui2-form-group-helper-text').describe('Error message');

  constructor(page: Page) {
    super(page);
  }
}

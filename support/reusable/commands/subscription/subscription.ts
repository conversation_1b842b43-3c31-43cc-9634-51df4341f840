import test, { expect, Page } from '@playwright/test';
import { DashboardPage } from '../../../pages/DashboardPage';
import { SubscriptionPromoPage } from '../../../pages/subscribtion/SubscriptionPromoPage';
import { SubscriptionTierTitle } from '../../../types';

export const performSubscription = async (
  page: Page,
  tariffName: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall
) => {
  await test.step(`Perform subscription to ${tariffName} tariff`, async () => {
    const subscriptionPromoPage = new SubscriptionPromoPage(page);
    await subscriptionPromoPage.goto();
    await subscriptionPromoPage.subscriptionCards.selectTariffByName(tariffName);

    const { subscriptionBuyModal } = subscriptionPromoPage;

    const termsAndConditionsCheckbox = page
      .locator('[data-cy="agreement"] > span')
      .describe('Terms and conditions checkbox');

    await expect(termsAndConditionsCheckbox).toBeVisible();
    await termsAndConditionsCheckbox.click();
    await subscriptionBuyModal.connectTariffBtn.click();

    await subscriptionBuyModal.purchaseCompleteTakeLaterButton.click();

    // eslint-disable-next-line playwright/no-conditional-in-test
    // if (tariffName === SubscriptionTierTitle.ExtraSmall) {
    //   await subscriptionBuyModal.purchaseCompleteTakeLaterButton.click();
    // } else {
    //   await subscriptionBuyModal.purchaseCompleteButton.click();
    // }

    await new DashboardPage(page).goto();
    await expect(page).toHaveURL(/.*dashboard.*/);
  });
};

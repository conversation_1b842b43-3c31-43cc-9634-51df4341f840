import { CreateCardSummary } from '../../../pages/widgets/issue-card/CreateCardSummary';
import { getExchangeRates } from '../../../helpers/page-js-helpers';
import { TCurrency, TCurrencyAccountSelect } from '../../../types';
import { expect, Page } from '@playwright/test';
import { test } from 'playwright/test';

// verifying total calculation in the summary widget is correct
export const haveCorrectTotalCalculated = async (
  page: Page,
  selectedAccount: TCurrency = TCurrencyAccountSelect.USD
) => {
  await test.step('Verify total for currency: ' + selectedAccount, async () => {
    const createCardSummary = new CreateCardSummary(page);

    const bonusAmount = await createCardSummary.getBonusAmount();
    const monthlyPayment = await createCardSummary.getMonthlyPaymentValue();
    const startingBalance = await createCardSummary.getStartingBalance();
    const topUpFee = await createCardSummary.getTopUpFee();
    const displayedTotal = await createCardSummary.getTotal();

    expect(monthlyPayment).toBeTruthy();
    expect(startingBalance).toBeTruthy();
    expect(displayedTotal).toBeTruthy();

    const apiExchangeRate = await getExchangeRates(page, selectedAccount);

    const commission = (startingBalance * topUpFee) / (1 - topUpFee);
    const expectedTotal = (commission + monthlyPayment + startingBalance) / apiExchangeRate;

    console.table({
      '---': '---',
      startingBalance: startingBalance,
      monthlyPayment: monthlyPayment,
      topUpFee: topUpFee,
      apiExchangeRate: apiExchangeRate,
      commission: commission,
      expectedTotal: expectedTotal,
      displayedTotal: displayedTotal,
      '----': '----',
      'comission:': `(${startingBalance} * ${topUpFee}) / (1 - ${topUpFee}) = ${commission}`,
      'comission expanded:': `(${startingBalance * topUpFee}) / (${1 - topUpFee}) = ${commission}`,
      '-----': '----',
      'expected total00:': `(${commission} + ${monthlyPayment} + ${startingBalance}) / ${apiExchangeRate} = ${expectedTotal}`,
      '-expectedTotal01:': `(${commission} + ${monthlyPayment + startingBalance}) / ${apiExchangeRate} = ${expectedTotal}`,
      '-expectedTotal02:': (commission + monthlyPayment + startingBalance) / apiExchangeRate,
      '-expectedTotal:': `(${commission} + ${monthlyPayment} + ${startingBalance}) / ${apiExchangeRate} = ${expectedTotal}`,
      '-expectedTotal2:': `(${
        commission + monthlyPayment + startingBalance
      }) / ${apiExchangeRate} = ${expectedTotal}`,
    });

    console.log(
      `DEBUG: expectedTotal (${expectedTotal}) = (commission [${commission}] + monthlyPayment [${monthlyPayment}] + startingBalance [${startingBalance}]) / 1`
    );

    console.log(
      `DEBUG expected total = (${commission} + ${monthlyPayment} + ${startingBalance}) / 1 `
    );

    expect(Math.abs(displayedTotal - expectedTotal) - bonusAmount).toBeLessThan(0.01);
  });
};

import { SumsubVerificationScreen } from '../../support/pages/widgets/SumsubVerificationScreen';
import { VerificationPage } from '../../support/pages/VerificationPage';
import { test } from '../../support/fixtures/user.fixture';
import { expect } from '@playwright/test';
import { apiUserSetExperiment } from '../../support/helpers/experiments';
import { ExperimentKeys } from '../../support/helpers/experiments';
import { getUserToken } from '../../support/helpers/page-js-helpers';

test.describe('Unlimited verification', { tag: ['@verification'] }, () => {
  test.describe('Logged in user', () => {
    test('Should show Sumsub iframe', async ({ loggedInUser }) => {
      const { page } = loggedInUser;
      // set backtonoveber 0
      await apiUserSetExperiment(await getUserToken(page), ExperimentKeys.BackToNovember, 0);
      const verificationPage = new VerificationPage(page);
      await verificationPage.goto();
      await verificationPage.availableVerificationScreen.clickUpgradeVerification();
      const frame = new SumsubVerificationScreen(page).getSumsubIframe();

      await expect(frame.locator('body')).toBeVisible();
      await expect(frame.getByRole('button', { name: 'Continue' })).toBeVisible();
      // We dont test the full flow here, just check that iframe is loaded
    });
  });
});

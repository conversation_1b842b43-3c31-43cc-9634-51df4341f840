import {
  CountrySetOne,
  performCountrySelect,
} from '../../support/reusable/commands/verification/country-select';
import { performSumsubScaleVerification } from '../../support/reusable/commands/verification/sumsub-scale-verification';
import { SumsubVerificationScreen } from '../../support/pages/widgets/SumsubVerificationScreen';
import { VerificationPage } from '../../support/pages/VerificationPage';
import { test } from '../../support/fixtures/user.fixture';
import { Settings } from '../../support/settings';
import { expect } from '@playwright/test';
import { VerificationTier } from '../../support/types';

const longTimeout = { timeout: Settings.timeouts.long };

test.describe.skip('Scale verification', { tag: ['@verification', '@unstable', '@skip'] }, () => {
  // sumsub docs upload is not stable, we must extend timeout for this test
  test.setTimeout(Settings.timeouts.long);
  test.beforeEach(async ({ verifiedUser }) => {
    const { page } = await verifiedUser(VerificationTier.Welcome);
    await test.step('Open Scale verification', async () => {
      const verificationPage = new VerificationPage(page);
      await verificationPage.goto();
      await verificationPage.availableVerificationScreen.clickUpgradeVerification();
    });
    await test.step('Select country', async () => {
      await performCountrySelect(CountrySetOne.AL, page, true);
    });
  });

  test('Success', async ({ page }) => {
    await test.step('Complete Sumsub', async () => {
      const iframe = new SumsubVerificationScreen(page).getSumsubIframe();
      await expect(iframe.locator('body')).toBeVisible(longTimeout);

      await performSumsubScaleVerification(page);

      const verificationPage = new VerificationPage(page);

      await expect(verificationPage.verificationApproved).toBeVisible({
        timeout: Settings.timeouts.medium,
      });

      await page.getByTestId('close-sidebar-button').click();
    });
    await test.step('Check verification status', async () => {
      const verificationPage = new VerificationPage(page);
      await verificationPage.goto();
      const text = await verificationPage.availableVerificationScreen.getCurrentVerificationName();
      expect(text?.toLowerCase()).toContain('scale');
    });
  });

  // This test is skipped because Sumsub is not stable and sometimes behaves unexpectedly.
  // eslint-disable-next-line playwright/no-skipped-test
  test.skip('Should fail - upload wrong image', async ({ page }) => {
    await test.step('Complete Sumsub', async () => {
      const iframe = new SumsubVerificationScreen(page).getSumsubIframe();
      // wait for iframe to be visible
      await expect(iframe.locator('body')).toBeVisible(longTimeout);

      // frame
      const contentFrame = page.locator('#sumframe').contentFrame();

      // wait for continue button
      const continueButton = contentFrame
        .getByRole('button', { name: 'Continue' })
        .describe('Continue button');
      await expect(continueButton).toBeVisible(longTimeout);
      await continueButton.click();

      // start
      const startVerificationButton = contentFrame
        .getByRole('button', {
          name: 'Start verification',
        })
        .describe('Start verification button');
      await expect(startVerificationButton).toBeVisible(longTimeout);
      await startVerificationButton.click();

      // agree
      const agreeButton = contentFrame
        .getByRole('button', { name: 'Agree and continue' })
        .describe('Agree button');
      await expect(agreeButton).toBeVisible(longTimeout);
      await agreeButton.click();

      // select id card as method
      await contentFrame.getByText('ID card').describe('Id card').click();

      // continue
      await contentFrame
        .getByRole('button', { name: 'Continue', exact: true })
        .describe('Continue')
        .click();

      // upload front image
      await contentFrame
        .getByRole('button', { name: 'Front side' })
        .describe('Front side')
        .setInputFiles('support/files/verification/broken-id.png');

      // upload back image
      await contentFrame
        .getByRole('button', { name: 'Back side' })
        .describe('Back side')
        .setInputFiles('support/files/verification/broken-id.png');

      const uploadAgainButton = contentFrame
        .getByRole('button', { name: 'Upload again' })
        .describe('Upload again');
      await expect(uploadAgainButton).toBeVisible(longTimeout);
    });
  });

  test('Should fail - upload rejected images', async ({ page }) => {
    await test.step('Complete Sumsub', async () => {
      const iframe = new SumsubVerificationScreen(page).getSumsubIframe();
      await expect(iframe.locator('body')).toBeVisible(longTimeout);

      // iframe verification
      const contentFrame = page.locator('#sumframe').contentFrame();

      // wait for continue button
      const continueButton = contentFrame.getByRole('button', { name: 'Continue' });
      await expect(continueButton).toBeVisible(longTimeout);
      await continueButton.click();

      // start
      const startVerificationButton = contentFrame.getByRole('button', {
        name: 'Start verification',
      });
      await expect(startVerificationButton).toBeVisible(longTimeout);
      await startVerificationButton.click();

      // agree
      const agreeButton = contentFrame.getByRole('button', { name: 'Agree and continue' });
      await expect(agreeButton).toBeVisible(longTimeout);
      await agreeButton.click();

      // select id card as method
      await contentFrame.getByText('ID card').click();

      // continue
      await contentFrame.getByRole('button', { name: 'Continue', exact: true }).click();

      // upload front image
      await contentFrame
        .getByRole('button', { name: 'Front side' })
        .setInputFiles('support/files/verification/reject-spain-id-back.png');

      // upload back image
      await contentFrame
        .getByRole('button', { name: 'Back side' })
        .setInputFiles('support/files/verification/reject-spain-id-front.png');

      // go on
      const continueButton2 = contentFrame.getByRole('button', { name: 'Continue' }).first();
      await expect(continueButton2).toBeVisible(longTimeout);
      await expect(continueButton2).toBeEnabled(longTimeout);
      await continueButton2.click();

      const resubmitButton = contentFrame.getByRole('button', { name: 'Resubmit' });
      await expect(resubmitButton).toBeVisible(longTimeout);

      const verificationPage = new VerificationPage(page);
      await verificationPage.goto();
      const text = await verificationPage.availableVerificationScreen.getCurrentVerificationName();
      expect(text?.toLowerCase()).not.toContain('scale');
    });
  });
});

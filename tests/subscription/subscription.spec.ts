import { DashboardPage } from '../../support/pages/DashboardPage';
import { SubscriptionDetailPage } from '../../support/pages/subscribtion/SubscriptionDetailPage';
import { SubscriptionPromoPage } from '../../support/pages/subscribtion/SubscriptionPromoPage';
import { expect, test } from '../../support/fixtures/user.fixture';
import {
  ExtensionNumberMap,
  SubscriptionExtensionTitle,
  SubscriptionTierTitle,
  VerificationTier,
} from '../../support/types';
import { waitForNetworkIdle } from '../../support/helpers/wait-for-network-idle';
import { SubscriptionCards } from '../../support/pages/widgets/subscription/subscriptionCards';
import { UserTariffExtensionTabsSelect } from '../../support/pages/widgets/ui/userTariffExtensionTabsSelect';
import { performBuySubscriptionExtension } from '../../support/reusable/commands/subscription/buy-subscription-extension';
import { confirmDialog } from '../../support/helpers/confirm-dialog';
import { Settings } from '../../support/settings';
import { extractNumberFromString } from '../../support/helpers/extract-number-from-string';

const { ExtraSmall, Small } = SubscriptionTierTitle;

test.describe('Subscription', { tag: ['@subscription'] }, () => {
  test.describe('With user with positive balance', () => {
    test('Should be able to add subscription', async ({ positiveUser, page }) => {
      await test.step('Prepare user', async () => {
        await positiveUser(VerificationTier.Scale);
      });

      await test.step('Open subscription page', async () => {
        await new SubscriptionPromoPage(page).goto();
      });

      const subscriptionPromoPage = new SubscriptionPromoPage(page);

      await test.step('Click on selected tariff', async () => {
        const card = await subscriptionPromoPage.subscriptionCards.getTariffCardByName(ExtraSmall);
        await card.scrollIntoViewIfNeeded();
        await expect(card).toBeVisible();
        const button = card.getByTestId('select-tariff-button');
        await expect(button).toBeVisible();
        await button.click();
      });

      await test.step('Connect account and confirm purchase', async () => {
        const { subscriptionBuyModal } = subscriptionPromoPage;
        await subscriptionBuyModal.termsAndConditionsCheckbox.click();
        await subscriptionBuyModal.connectTariffBtn.click();
        await subscriptionBuyModal.purchaseCompleteTakeLaterButton.click();
      });

      await test.step('Check subscription is added', async () => {
        const subscriptionDetailPage = new SubscriptionDetailPage(page);
        await subscriptionDetailPage.goto();
        await expect(subscriptionDetailPage.currentTariffTitle).toHaveText(ExtraSmall);
      });
    });

    // TODO: bring back after tabs are back
    test.skip('Should be able to switch between subscription tabs', async ({
      positiveUser,
      page,
    }) => {
      let monthlyTariffs: number[] = [];
      const monthlyTariffsSlugs = ['Extra Small', 'Small_A', 'Medium_A', 'Large_A'];
      let quarterlyTariffs: number[] = [];
      const quarterlyTariffsSlugs = ['Extra Small_3', 'Small_3A', 'Medium_3A', 'Large_3A'];
      let semiannualTariffs: number[] = [];
      const semiannualTariffsSlugs = ['Extra Small_6', 'Small_6A', 'Medium_6A', 'Large_6A'];

      await test.step('Prepare user', async () => {
        await positiveUser(VerificationTier.Scale);
      });

      const subscriptionPromoPage = new SubscriptionPromoPage(page);

      await page.route('**/subscriptions/tariffs**', async (route) => {
        const response = await route.fetch();
        const json = await response.json();
        const tariffs = json?.data || [];

        if (monthlyTariffs.length || quarterlyTariffs.length || semiannualTariffs.length) {
          await route.fulfill({ response });
          return;
        }

        monthlyTariffs = tariffs
          .filter(
            (t: { renewal_name: string; type_name: string }) =>
              t.renewal_name === 'MONTHLY' && t.type_name === 'MAIN'
          )
          .filter((t: { slug: string }) => monthlyTariffsSlugs.includes(t.slug))
          .map((t: { amount: unknown }) => Number(t.amount))
          .sort((a: number, b: number) => a - b);

        quarterlyTariffs = tariffs
          .filter(
            (t: { renewal_name: string; type_name: string }) =>
              t.renewal_name === 'QUARTER' && t.type_name === 'MAIN'
          )
          .filter((t: { slug: string }) => quarterlyTariffsSlugs.includes(t.slug))
          .map((t: { amount: unknown }) => Math.floor(Number(t.amount) / 3))
          .sort((a: number, b: number) => a - b);

        semiannualTariffs = tariffs
          .filter(
            (t: { renewal_name: string; type_name: string }) =>
              t.renewal_name === 'HALF_YEAR' && t.type_name === 'MAIN'
          )
          .filter((t: { slug: string }) => semiannualTariffsSlugs.includes(t.slug))
          .map((t: { amount: unknown }) => Math.floor(Number(t.amount) / 6))
          .sort((a: number, b: number) => a - b);

        await route.fulfill({ response });
      });

      await test.step('Open subscription page', async () => {
        await subscriptionPromoPage.goto();
        await waitForNetworkIdle(page);
      });

      const cardsWidget = subscriptionPromoPage.subscriptionCards;

      await test.step('Check monthly tab tariffs', async () => {
        await cardsWidget.selectTariffTab('monthly');
        const pricesTexts = await cardsWidget.getDisplayedCardsPrices();
        const prices = pricesTexts?.map((text) => extractNumberFromString(text));
        expect(prices).toEqual(monthlyTariffs);
      });

      await test.step('Switch to quarterly tab', async () => {
        await cardsWidget.selectTariffTab('quarterly');

        const pricesTexts = await cardsWidget.page
          .getByTestId(SubscriptionCards.SUBSCRIPTION_TARIFF_CARD_ID)
          .getByTestId(SubscriptionCards.TARIFF_PRICE_ID)
          .allTextContents();
        const prices = pricesTexts?.map((text) => extractNumberFromString(text));

        expect(prices).toEqual(quarterlyTariffs);
      });

      await test.step('Switch to semiannual tab', async () => {
        await cardsWidget.selectTariffTab('semiannual');
        const displayedTariffPrices = await cardsWidget.getDisplayedCardsPrices();
        const displayedNumbers = displayedTariffPrices.map((p) => extractNumberFromString(p));
        expect(displayedNumbers).toEqual(semiannualTariffs);
      });
    });
  });

  test.describe('With user with subscription', () => {
    test.setTimeout(Settings.timeouts.extraLong);
    test.beforeEach(async ({ withSubscription }) => {
      await withSubscription(SubscriptionTierTitle.ExtraSmall);
    });

    test('Should be able to cancel subscription', async ({ page }) => {
      const subscriptionDetailPage = new SubscriptionDetailPage(page);

      await test.step('Open Manage modal', async () => {
        await subscriptionDetailPage.goto();
        await subscriptionDetailPage.manageSubscriptionButton.click();
      });

      await test.step('Cancel subscription', async () => {
        const { privateManagementModal } = subscriptionDetailPage;
        await privateManagementModal.dotsVerticalButton.click();
        await privateManagementModal.cancelSubscriptionButton.click();
        await privateManagementModal.cancelSubscriptionButtonLosePrivate.click();
        await privateManagementModal.cancelSubscriptionButtonAnyWay.click();
        await privateManagementModal.cancelByReason(1);
      });

      await test.step('Check subscription is removed', async () => {
        const dashboardPage = new DashboardPage(page);
        await dashboardPage.goto();
        await waitForNetworkIdle(page);
        await expect(dashboardPage.subscriptionBlock).toBeHidden();
        await subscriptionDetailPage.goto();
        await waitForNetworkIdle(page);
        await expect(subscriptionDetailPage.currentTariffBlock).toBeHidden();
      });
    });

    test('Should be able to switch subscription plan', async ({ page }) => {
      await test.step('Open Manage modal', async () => {
        const subscriptionDetailPage = new SubscriptionDetailPage(page);
        await subscriptionDetailPage.goto();
        await subscriptionDetailPage.manageSubscriptionButton.click();
      });

      await test.step('Switch plan', async () => {
        const { privateManagementModal } = new SubscriptionDetailPage(page);
        await privateManagementModal.upgradeButton.click();
      });

      await test.step('Select new plan and confirm', async () => {
        const cardsWidget = new SubscriptionCards(page);
        await cardsWidget.selectTariffByName(Small);
        const subscriptionDetailPage = new SubscriptionDetailPage(page);
        await subscriptionDetailPage.upgradeConnectButton.click();
        await subscriptionDetailPage.modalSuccessButton.click();
      });

      await test.step('Check new plan is active', async () => {
        const subscriptionDetailPage = new SubscriptionDetailPage(page);
        await subscriptionDetailPage.goto();
        await expect(subscriptionDetailPage.currentTariffTitle).toHaveText(Small);
      });
    });

    test('Should be able to add extension for tariff', async ({ page }) => {
      const subscriptionDetailPage = new SubscriptionDetailPage(page);
      const { TenCards } = SubscriptionExtensionTitle;
      let availableCards = 0;

      await test.step('Open Raise limit modal', async () => {
        await subscriptionDetailPage.goto();

        availableCards = await subscriptionDetailPage.getTotalAvailableCards();

        await subscriptionDetailPage.manageSubscriptionButton.click();
        await subscriptionDetailPage.privateManagementModal.raiseLimitButtonNoExtensions.click();
      });

      await test.step('Select tariff extension', async () => {
        const tabsSelect = new UserTariffExtensionTabsSelect(page);
        await tabsSelect.getTabByName(TenCards).click();
        await page.getByTestId('tariff-extension-continue-btn').click();
        await page.getByTestId('tariff-extension-confirm-btn').click();
        await page.getByTestId('tariff-extension-later-btn').click();
      });

      await test.step('Check extension is added', async () => {
        await subscriptionDetailPage.goto();
        const newAvailableCards = await subscriptionDetailPage.getTotalAvailableCards();
        await expect(subscriptionDetailPage.currentTariffBlock).toBeVisible();

        const expectedCards = availableCards + ExtensionNumberMap[TenCards];
        expect(newAvailableCards).toEqual(expectedCards);
      });
    });

    test('Should be able to remove extension for tariff', async ({ page }) => {
      await test.step('Add extension for tariff', async () => {
        await performBuySubscriptionExtension(page, SubscriptionExtensionTitle.TenCards);
      });

      const subscriptionDetailPage = new SubscriptionDetailPage(page);

      await test.step('Open management modal', async () => {
        await subscriptionDetailPage.goto();
        await subscriptionDetailPage.manageSubscriptionButton.click();
      });

      await test.step('Cancel extension', async () => {
        const { privateManagementModal } = subscriptionDetailPage;
        const cards = privateManagementModal.extensionCards;
        const firstCard = cards.first();
        await expect(firstCard).toBeVisible();
        await firstCard.getByTestId('dots-vertical-icon').click();
        await privateManagementModal.page.getByTestId('cancel-subscription-btn').click();

        await confirmDialog(privateManagementModal.page);

        // we wait until all network requests are done, otherwise it won't cancel extension
        await waitForNetworkIdle(page);
      });

      await test.step('Check extension is removed', async () => {
        await subscriptionDetailPage.goto();
        const newAvailableCards = await subscriptionDetailPage.getTotalAvailableCards();
        expect(newAvailableCards).toEqual(1);
      });
    });
  });
});


#export type TCreateUserPayload = {
#user: {
#email: string;
#password: string;
#master_id?: number;
#    type?: number;
#  };
#  verification: {
#    slug?: VerificationTier;
#    first_name?: string;
#    last_name?: string;
#    birthday?: string;
#    country_iso3?: string;
#  };
#  country_set?: {
#    id: TCountrySet;
#  };
#  is_suspicious?: boolean;
#  show_warn?: boolean;
#};

### GET request to example server
POST https://emulate.pstage.net/api/user-register/site
Content-Type: application/json

{
  "user": {
    "email": "<EMAIL>",
    "password": "<EMAIL>"
  },
  "country_set": {
    "id": 1
  }
}
###